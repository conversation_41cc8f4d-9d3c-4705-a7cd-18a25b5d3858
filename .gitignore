# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Production builds
dist/
build/

# Environment variables
.env
.env.local
.env.*.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Coverage and test results
coverage/
*.lcov
.nyc_output
test-results/
jest-coverage/

# Temporary files
tmp/
temp/
*.tmp
*.bak

# Cache directories
.cache
.parcel-cache
.eslintcache

# Build analyzer
bundle-analyzer-report.html

# Cypress
cypress/videos/
cypress/screenshots/

# Documentation build files (keep markdown docs)
docs/build/
