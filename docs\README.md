# PhotoEditor Demo 文档中心

欢迎来到 PhotoEditor Demo 项目的文档中心！这里包含了项目的完整文档，按照不同的用途和受众进行了分类组织。

## 📚 文档导航

### 👥 用户指南
适合最终用户和想要快速上手的开发者

- **[用户指南](user-guide/README.md)** - 完整的用户使用指南
  - [快速开始](user-guide/getting-started.md) - 项目安装和基本使用
  - [使用指南](user-guide/usage-guide.md) - 详细的功能使用说明
  - [浏览器兼容性](user-guide/browser-compatibility.md) - 支持的浏览器和版本

### 👨‍💻 开发者指南
适合想要深入了解项目架构和进行二次开发的开发者

- **[开发者指南](developer-guide/README.md)** - 完整的开发者文档
  - **[架构设计](developer-guide/architecture/README.md)** - 项目架构和设计理念
    - [适配器设计](developer-guide/architecture/adapter-design.md) - 多库适配器架构
    - [状态管理](developer-guide/architecture/state-management.md) - 状态管理系统
    - [UI组件](developer-guide/architecture/ui-components.md) - UI组件设计
  - **[API参考](developer-guide/api-reference/README.md)** - 详细的API文档
    - [组件API](developer-guide/api-reference/components.md) - 组件接口文档
    - [适配器API](developer-guide/api-reference/adapters.md) - 适配器接口文档
  - **[开发相关](developer-guide/development/README.md)** - 开发环境和流程
    - [开发环境设置](developer-guide/development/setup.md) - 开发环境配置
    - [贡献指南](developer-guide/development/contributing.md) - 如何参与项目开发
    - [开发路线图](developer-guide/development/roadmap.md) - 项目发展规划

### 🚀 部署指南
适合需要部署项目的运维人员和开发者

- **[部署指南](deployment/README.md)** - 项目部署相关文档
  - [GitHub设置](deployment/github-setup.md) - GitHub仓库设置和发布
  - [生产环境构建](deployment/production-build.md) - 生产环境部署指南

### 🔧 故障排除
适合遇到问题需要解决方案的用户和开发者

- **[故障排除](troubleshooting/README.md)** - 问题解决和优化指南
  - [常见问题](troubleshooting/common-issues.md) - 常见问题和解决方案
  - [性能优化](troubleshooting/performance-optimization.md) - 性能优化建议
  - **[修复记录](troubleshooting/bug-fixes/README.md)** - 历史问题修复记录
    - [Konva相关修复](troubleshooting/bug-fixes/konva-fixes.md) - Konva.js相关问题修复
    - [Fabric相关修复](troubleshooting/bug-fixes/fabric-fixes.md) - Fabric.js相关问题修复
    - [裁剪功能修复](troubleshooting/bug-fixes/cropping-fixes.md) - 图片裁剪功能修复
    - [通用修复](troubleshooting/bug-fixes/general-fixes.md) - 其他通用问题修复

## 🎯 快速导航

### 我是新用户，想要快速体验项目
👉 [快速开始](user-guide/getting-started.md)

### 我想了解项目的功能和使用方法
👉 [使用指南](user-guide/usage-guide.md)

### 我是开发者，想要了解项目架构
👉 [架构设计](developer-guide/architecture/README.md)

### 我想要进行二次开发
👉 [开发者指南](developer-guide/README.md)

### 我想要部署这个项目
👉 [部署指南](deployment/README.md)

### 我遇到了问题需要解决
👉 [故障排除](troubleshooting/README.md)

## 📖 文档贡献

如果您发现文档中有错误或需要改进的地方，欢迎：

1. 提交 Issue 报告问题
2. 提交 Pull Request 改进文档
3. 在社区中分享您的使用经验

## 📞 获取帮助

- **GitHub Issues**: [提交问题](https://github.com/LuoLeYan/photoEditorDemo/issues)
- **项目主页**: [PhotoEditor Demo](https://github.com/LuoLeYan/photoEditorDemo)

---

*最后更新时间: 2025-07-15*
