{"name": "photoEditorDemo", "version": "1.0.0", "description": "A comprehensive Vue.js image editor demo showcasing professional image editing components with multi-library adapter architecture", "author": "LuoLe<PERSON>an", "license": "MIT", "keywords": ["vue", "image-editor", "photo-editor", "canvas", "graphics", "konva", "fabric", "tui-image-editor", "cropper", "filters"], "repository": {"type": "git", "url": "https://github.com/LuoLeYan/photoEditorDemo.git"}, "homepage": "https://luoleyan.github.io/photoEditorDemo", "private": false, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "jest", "test:unit:watch": "jest --watch", "test:unit:coverage": "jest --coverage", "test:unit:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"core-js": "^3.8.3", "cropperjs": "^1.6.1", "fabric": "^5.3.0", "jimp": "^0.22.10", "konva": "^9.2.0", "tui-image-editor": "^3.15.3", "vue": "^2.6.14", "vue-router": "^3.5.1", "vuedraggable": "^2.24.3", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/test-utils": "^1.3.6", "@vue/vue2-jest": "^27.0.0", "babel-jest": "^27.5.1", "identity-obj-proxy": "^3.0.0", "jest": "^27.5.1", "jest-environment-jsdom": "^27.5.1", "jest-transform-stub": "^2.0.0", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}