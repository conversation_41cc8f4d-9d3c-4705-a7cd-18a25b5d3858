# 常见问题

本文档收集了用户在使用 PhotoEditor Demo 过程中经常遇到的问题及其解决方案。

## 🚨 紧急问题

### 应用无法启动

**问题描述**: 运行 `npm run serve` 后应用无法正常启动

**可能原因**:
- Node.js版本不兼容
- 依赖安装不完整
- 端口被占用
- 权限问题

**解决方案**:

1. **检查Node.js版本**
```bash
node --version
# 确保版本 >= 14.0
```

2. **清理并重新安装依赖**
```bash
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

3. **检查端口占用**
```bash
# Windows
netstat -ano | findstr :8080

# macOS/Linux
lsof -i :8080
```

4. **使用不同端口**
```bash
npm run serve -- --port 3000
```

### 图片无法加载

**问题描述**: 选择图片后无法在编辑器中显示

**可能原因**:
- 图片格式不支持
- 图片文件过大
- 浏览器安全限制
- 文件路径问题

**解决方案**:

1. **检查图片格式**
   - 支持格式：JPG、PNG、GIF、SVG
   - 不支持：WEBP、TIFF、RAW等

2. **检查图片大小**
   - 建议大小：< 5MB
   - 最大支持：10MB

3. **检查浏览器控制台**
   - 按F12打开开发者工具
   - 查看Console标签页的错误信息

4. **尝试不同图片**
   - 使用项目提供的示例图片测试
   - 尝试更小的图片文件

## 🔧 功能问题

### 编辑操作无响应

**问题描述**: 点击编辑按钮或调整参数时没有反应

**可能原因**:
- 图片未完全加载
- 浏览器性能不足
- JavaScript错误
- 适配器初始化失败

**解决方案**:

1. **等待图片加载完成**
   - 确保图片完全显示后再进行编辑
   - 查看加载进度指示器

2. **刷新页面重试**
```bash
# 硬刷新（清除缓存）
Ctrl+F5 (Windows)
Cmd+Shift+R (Mac)
```

3. **检查浏览器兼容性**
   - 使用推荐的浏览器版本
   - 查看 [浏览器兼容性](../user-guide/browser-compatibility.md)

4. **查看控制台错误**
   - 检查是否有JavaScript错误
   - 查看网络请求是否正常

### 下载功能异常

**问题描述**: 点击下载按钮后没有文件下载或下载的文件损坏

**可能原因**:
- 浏览器下载被阻止
- 图片处理未完成
- 文件格式问题
- 浏览器安全设置

**解决方案**:

1. **检查浏览器下载设置**
   - 允许弹窗和下载
   - 检查下载文件夹权限

2. **等待处理完成**
   - 确保所有编辑操作已完成
   - 等待处理进度条结束

3. **尝试不同格式**
   - PNG格式（推荐）
   - JPG格式
   - 调整质量设置

4. **右键保存**
   - 右键点击预览图片
   - 选择"图片另存为"

## 📱 移动端问题

### 触摸操作不准确

**问题描述**: 在移动设备上触摸操作不够精确

**解决方案**:

1. **使用缩放功能**
   - 双指缩放放大操作区域
   - 提高操作精度

2. **调整触摸设置**
   - 关闭其他应用释放内存
   - 清理屏幕确保触摸准确

3. **使用简化模式**
   - 选择移动端优化的编辑库
   - 使用较大的控制按钮

### 性能问题

**问题描述**: 移动设备上应用运行缓慢或卡顿

**解决方案**:

1. **使用较小的图片**
   - 建议 < 2MB
   - 降低图片分辨率

2. **关闭其他应用**
   - 释放设备内存
   - 关闭后台应用

3. **选择合适的编辑库**
   - Cropper.js：轻量级，适合移动端
   - 避免使用复杂的滤镜效果

## 🌐 浏览器特定问题

### Safari 问题

**问题描述**: 在Safari浏览器中某些功能异常

**常见问题**:
- 文件下载被阻止
- Canvas渲染问题
- 内存限制

**解决方案**:

1. **启用弹窗和下载**
   - Safari设置 > 网站 > 弹出式窗口
   - 允许当前网站的下载

2. **清理Safari缓存**
   - Safari > 偏好设置 > 隐私 > 管理网站数据

3. **更新Safari版本**
   - 确保使用最新版本

### Firefox 问题

**问题描述**: Firefox中性能较差或功能异常

**解决方案**:

1. **禁用扩展**
   - 临时禁用浏览器扩展
   - 测试是否影响功能

2. **调整Firefox设置**
   - about:config
   - 搜索 `canvas` 相关设置

3. **使用Firefox开发者版本**
   - 获得更好的开发工具支持

## 🔍 调试技巧

### 开发者工具使用

1. **Console面板**
```javascript
// 查看错误信息
console.error()

// 查看适配器状态
window.imageEditor?.getState()

// 性能监控
console.time('operation')
// ... 执行操作
console.timeEnd('operation')
```

2. **Network面板**
   - 检查资源加载情况
   - 查看API请求状态
   - 监控文件上传进度

3. **Performance面板**
   - 分析性能瓶颈
   - 查看内存使用情况
   - 监控帧率

### 常用调试命令

```javascript
// 启用调试模式
localStorage.setItem('debug', 'true')

// 查看当前适配器
console.log(window.currentAdapter)

// 查看图片信息
console.log(window.currentImage)

// 清除本地存储
localStorage.clear()
```

## 📊 问题统计

### 最常见问题 (Top 10)

1. 图片加载失败 (25%)
2. 浏览器兼容性 (20%)
3. 性能问题 (15%)
4. 下载功能异常 (12%)
5. 移动端操作问题 (10%)
6. 安装依赖失败 (8%)
7. 端口占用 (5%)
8. 权限问题 (3%)
9. 网络连接问题 (2%)

### 解决率统计

- 自助解决: 70%
- 查看文档: 20%
- 社区求助: 8%
- 提交Issue: 2%

## 💡 预防建议

### 用户建议

1. **使用推荐环境**
   - 最新版本浏览器
   - 稳定的网络连接
   - 充足的设备内存

2. **合理使用功能**
   - 适当大小的图片
   - 避免过度复杂的操作
   - 定期保存进度

3. **定期维护**
   - 清理浏览器缓存
   - 更新浏览器版本
   - 关闭不必要的标签页

### 开发者建议

1. **代码质量**
   - 添加错误处理
   - 实现优雅降级
   - 提供用户反馈

2. **性能优化**
   - 懒加载资源
   - 优化图片处理
   - 减少内存占用

3. **用户体验**
   - 清晰的错误提示
   - 完善的加载状态
   - 友好的操作指引

## 📞 获取更多帮助

### 自助资源

- [使用指南](../user-guide/usage-guide.md) - 详细功能说明
- [浏览器兼容性](../user-guide/browser-compatibility.md) - 兼容性信息
- [修复记录](bug-fixes/README.md) - 历史问题修复

### 社区支持

- **GitHub Issues**: [提交问题](https://github.com/LuoLeYan/photoEditorDemo/issues)
- **讨论区**: GitHub Discussions
- **邮件支持**: 技术支持邮箱

### 报告问题

提交问题时请包含：
- 详细的问题描述
- 重现步骤
- 浏览器和版本信息
- 控制台错误截图
- 操作系统信息

---

*问题没有解决？查看 [修复记录](bug-fixes/README.md) 或在 GitHub 上提交新的 Issue。*
