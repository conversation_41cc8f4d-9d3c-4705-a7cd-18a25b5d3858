# 快速开始

欢迎使用 PhotoEditor Demo！本指南将帮助您快速上手这个功能强大的图像编辑演示应用。

## 🚀 安装和运行

### 环境要求

在开始之前，请确保您的系统满足以下要求：

- **Node.js**: 14.0 或更高版本
- **npm**: 6.0 或更高版本（或使用 yarn）
- **现代浏览器**: Chrome 60+、Firefox 55+、Safari 12+、Edge 79+

### 获取项目

#### 方法一：克隆仓库

```bash
# 克隆项目到本地
git clone https://github.com/LuoLeYan/photoEditorDemo.git

# 进入项目目录
cd photoEditorDemo
```

#### 方法二：下载压缩包

1. 访问 [GitHub 项目页面](https://github.com/LuoLeYan/photoEditorDemo)
2. 点击 "Code" 按钮
3. 选择 "Download ZIP"
4. 解压到本地目录

### 安装依赖

```bash
# 使用 npm 安装依赖
npm install

# 或使用 yarn
yarn install
```

### 启动开发服务器

```bash
# 启动开发服务器
npm run serve

# 或使用 yarn
yarn serve
```

启动成功后，您将看到类似以下的输出：

```
App running at:
- Local:   http://localhost:8080/
- Network: http://*************:8080/
```

在浏览器中打开 `http://localhost:8080/` 即可开始使用！

## 🎯 基本操作

### 选择编辑库

PhotoEditor Demo 集成了5个不同的图像编辑库，每个都有其独特的优势：

1. **TUI Image Editor** - 完整UI界面，功能最全面
2. **Fabric.js** - 高度可定制，适合复杂编辑
3. **Cropper.js** - 专业裁剪，轻量高效
4. **Jimp** - 纯JavaScript，适合批量处理
5. **Konva.js** - 高性能渲染，动画丰富

### 加载图片

1. 点击页面上的"加载图片"按钮
2. 从文件选择器中选择图片文件
3. 支持的格式：JPG、PNG、GIF、SVG
4. 建议图片大小不超过 5MB

### 基本编辑操作

#### 亮度调节

- 使用亮度滑块调整图片亮度
- 范围：-1（最暗）到 +1（最亮）
- 实时预览效果

#### 对比度调节

- 使用对比度滑块调整图片对比度
- 范围：-1（最低对比度）到 +1（最高对比度）
- 实时预览效果

#### 图片旋转

- 使用角度滑块精确控制旋转角度
- 或使用快速旋转按钮（90度增量）
- 支持任意角度旋转

#### 图片裁剪

1. 启用裁剪模式
2. 拖拽调整裁剪区域
3. 可选择预设的裁剪比例
4. 点击应用完成裁剪

### 保存结果

1. 完成编辑后，点击"下载图片"按钮
2. 浏览器会自动下载编辑后的图片
3. 默认格式为 PNG，保持最佳质量

## 🎨 选择合适的编辑库

### 根据需求选择

| 需求场景 | 推荐库 | 原因 |
|---------|--------|------|
| 快速集成完整编辑器 | TUI Image Editor | 提供完整UI，开箱即用 |
| 复杂图形编辑 | Fabric.js | 高度可定制，功能强大 |
| 专业图片裁剪 | Cropper.js | 裁剪功能专业，性能优秀 |
| 批量图片处理 | Jimp | 纯JavaScript，适合自动化 |
| 动画和交互效果 | Konva.js | 高性能渲染，动画丰富 |

### 性能对比

| 库名称 | 文件大小 | 加载速度 | 操作响应 | 移动端适配 |
|--------|----------|----------|----------|------------|
| TUI Image Editor | 大 | 中等 | 良好 | 良好 |
| Fabric.js | 中等 | 快 | 优秀 | 良好 |
| Cropper.js | 小 | 很快 | 优秀 | 优秀 |
| Jimp | 中等 | 中等 | 中等 | 一般 |
| Konva.js | 中等 | 快 | 优秀 | 良好 |

## 📱 移动端使用

### 触摸操作

- **缩放**: 双指捏合/分开
- **平移**: 单指拖拽
- **旋转**: 双指旋转手势
- **裁剪**: 拖拽裁剪框边缘

### 性能建议

- 使用较小的图片（建议 2MB 以下）
- 避免同时进行多个复杂操作
- 定期清理浏览器缓存

## 🔧 故障排除

### 常见问题

#### 应用无法启动

```bash
# 清除缓存并重新安装
rm -rf node_modules package-lock.json
npm install
npm run serve
```

#### 图片无法加载

1. 检查图片格式是否支持
2. 确认图片大小不超过 5MB
3. 查看浏览器控制台错误信息

#### 编辑功能异常

1. 刷新页面重新加载
2. 检查浏览器版本是否支持
3. 尝试使用其他编辑库

### 获取帮助

- 查看 [使用指南](usage-guide.md) 了解详细功能
- 查看 [故障排除](../troubleshooting/README.md) 解决问题
- 在 [GitHub](https://github.com/LuoLeYan/photoEditorDemo/issues) 上提交 Issue

## 🎓 下一步

现在您已经掌握了基本操作，可以：

1. 查看 [详细使用指南](usage-guide.md) 了解更多功能
2. 探索不同编辑库的特色功能
3. 查看 [开发者指南](../developer-guide/README.md) 进行二次开发

---

*准备好探索更多功能了吗？查看 [使用指南](usage-guide.md) 了解详细的功能说明！*
